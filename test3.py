
import torch
import torchvision
import torch.nn as nn
import torch.optim as optim
from torchvision import transforms, models
import time

# 设置设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# 数据预处理
transform_train = transforms.Compose([
    transforms.RandomHorizontalFlip(),
    transforms.RandomCrop(32, padding=4),
    transforms.ToTensor(),
    transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010))
])

transform_test = transforms.Compose([
    transforms.ToTensor(),
    transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010))
])

# 加载CIFAR-10数据集
trainset = torchvision.datasets.CIFAR10(root='./data', train=True, download=True, transform=transform_train)
trainloader = torch.utils.data.DataLoader(trainset, batch_size=128, shuffle=True, num_workers=2)

testset = torchvision.datasets.CIFAR10(root='./data', train=False, download=True, transform=transform_test)
testloader = torch.utils.data.DataLoader(testset, batch_size=100, shuffle=False, num_workers=2)

def create_densenet_cifar10(growth_rate=32):
    """创建适配CIFAR-10的DenseNet-121"""
    model = models.DenseNet(
        growth_rate=growth_rate,
        block_config=(6, 12, 24, 16),  # DenseNet-121的配置
        num_init_features=64,
        bn_size=4,
        drop_rate=0,
        num_classes=10
    )

    # 修改第一层卷积以适配CIFAR-10的32x32输入
    model.features.conv0 = nn.Conv2d(3, 64, kernel_size=3, stride=1, padding=1, bias=False)
    # 移除初始的最大池化层
    model.features.pool0 = nn.Identity()

    return model

def train_and_test(growth_rate, epochs=5):
    """训练和测试模型"""
    print(f"\n=== Training DenseNet with Growth Rate {growth_rate} ===")

    # 创建新的模型实例
    model = create_densenet_cifar10(growth_rate=growth_rate).to(device)

    # 计算参数数量
    param_count = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Model parameters: {param_count:,}")

    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)

    # 训练循环
    for epoch in range(epochs):
        model.train()
        running_loss = 0.0
        correct_train = 0
        total_train = 0

        start_time = time.time()

        for i, (inputs, labels) in enumerate(trainloader):
            inputs, labels = inputs.to(device), labels.to(device)

            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()

            running_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total_train += labels.size(0)
            correct_train += (predicted == labels).sum().item()

            if i % 100 == 99:
                print(f'[Epoch {epoch+1}, Batch {i+1}] Loss: {running_loss/100:.4f}')
                running_loss = 0.0

        # 计算训练准确率
        train_acc = 100 * correct_train / total_train

        # 测试阶段
        model.eval()
        correct_test = 0
        total_test = 0

        with torch.no_grad():
            for inputs, labels in testloader:
                inputs, labels = inputs.to(device), labels.to(device)
                outputs = model(inputs)
                _, predicted = torch.max(outputs, 1)
                total_test += labels.size(0)
                correct_test += (predicted == labels).sum().item()

        test_acc = 100 * correct_test / total_test
        epoch_time = time.time() - start_time

        print(f'Epoch [{epoch+1}/{epochs}] - Time: {epoch_time:.2f}s')
        print(f'Train Acc: {train_acc:.2f}% | Test Acc: {test_acc:.2f}%')
        print('-' * 50)

    return test_acc, param_count

# 测试不同growth_rate
results = {}
growth_rates = [12, 24, 32]

print("Starting DenseNet-121 CIFAR-10 experiments with different growth rates...")

for gr in growth_rates:
    final_acc, params = train_and_test(gr, epochs=5)
    results[gr] = {'accuracy': final_acc, 'parameters': params}

# 打印结果总结
print(f"\n{'='*60}")
print("EXPERIMENT SUMMARY")
print(f"{'='*60}")
print(f"{'Growth Rate':<12} {'Parameters':<12} {'Final Test Acc':<15}")
print("-" * 40)
for gr in growth_rates:
    print(f"{gr:<12} {results[gr]['parameters']:<12,} {results[gr]['accuracy']:<15.2f}%")

best_gr = max(growth_rates, key=lambda x: results[x]['accuracy'])
print(f"\nBest performing growth rate: {best_gr} with {results[best_gr]['accuracy']:.2f}% accuracy")
print("Experiment completed!")
