
import torch
import torchvision
import torch.nn as nn
import torch.optim as optim
from torchvision import transforms

# 数据预处理
transform = transforms.Compose([
    transforms.RandomHorizontalFlip(),
    transforms.ToTensor(),
    transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
])

# 加载CIFAR-10
trainset = torchvision.datasets.CIFAR10(root='./data', train=True, download=True, transform=transform)
trainloader = torch.utils.data.DataLoader(trainset, batch_size=64, shuffle=True)

# 修改DenseNet-121第一层（适配32x32输入）
model = torchvision.models.densenet121(pretrained=False)
model.features.conv0 = nn.Conv2d(3, 64, kernel_size=3, stride=1, padding=1, bias=False)
model.features.pool0 = nn.Identity()  # 移除初始池化层

# 调整分类头
model.classifier = nn.Linear(1024, 10)

# 训练函数
def train(growth_rate):
    for param in model.parameters():
        param.requires_grad = True
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    criterion = nn.CrossEntropyLoss()

    for epoch in range(10):
        for inputs, labels in trainloader:
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
        print(f'Growth Rate {growth_rate}, Epoch {epoch+1}, Loss: {loss.item():.4f}')

# 测试不同growth_rate
for gr in [12, 24, 32]:
    train(gr)
