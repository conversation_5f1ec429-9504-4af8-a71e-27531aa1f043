import torch
import torch.nn as nn
import torch.optim as optim
import torchvision
from torchvision import transforms, models
import matplotlib.pyplot as plt
import numpy as np
from collections import defaultdict

# 设置设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

def create_densenet_cifar10(growth_rate=32):
    """创建适配CIFAR-10的DenseNet-121"""
    model = models.DenseNet(
        growth_rate=growth_rate,
        block_config=(6, 12, 24, 16),  # DenseNet-121的配置
        num_init_features=64,
        bn_size=4,
        drop_rate=0,
        num_classes=10
    )

    # 修改第一层卷积以适配CIFAR-10的32x32输入
    model.features.conv0 = nn.Conv2d(3, 64, kernel_size=3, stride=1, padding=1, bias=False)
    # 移除初始的最大池化层
    model.features.pool0 = nn.Identity()

    return model

def analyze_gradient_flow(model, dataloader, criterion, num_batches=5):
    """分析DenseNet中的梯度流"""
    model.train()
    gradient_stats = defaultdict(list)

    print("Analyzing gradient flow...")

    for batch_idx, (inputs, targets) in enumerate(dataloader):
        if batch_idx >= num_batches:
            break

        inputs, targets = inputs.to(device), targets.to(device)

        # 前向传播
        outputs = model(inputs)
        loss = criterion(outputs, targets)

        # 反向传播
        model.zero_grad()
        loss.backward()

        # 收集梯度统计信息
        for name, param in model.named_parameters():
            if param.grad is not None:
                grad_norm = torch.norm(param.grad, p=2).item()
                gradient_stats[name].append(grad_norm)

    return gradient_stats

def plot_gradient_analysis(gradient_stats, growth_rate):
    """可视化梯度分析结果"""
    # 计算平均梯度范数
    avg_gradients = {}
    for name, grads in gradient_stats.items():
        avg_gradients[name] = np.mean(grads)

    # 按层分类
    conv_layers = {}
    bn_layers = {}

    for name, grad_norm in avg_gradients.items():
        if 'conv' in name:
            conv_layers[name] = grad_norm
        elif 'norm' in name:
            bn_layers[name] = grad_norm

    # 绘制卷积层梯度
    if conv_layers:
        plt.figure(figsize=(15, 10))

        # 子图1: 卷积层梯度范数
        plt.subplot(2, 2, 1)
        names = list(conv_layers.keys())
        values = list(conv_layers.values())

        # 只显示前20个层以避免过于拥挤
        if len(names) > 20:
            names = names[:20]
            values = values[:20]

        plt.bar(range(len(names)), values, color='skyblue')
        plt.xticks(range(len(names)), [name.split('.')[-1] for name in names], rotation=45)
        plt.ylabel('Average Gradient L2 Norm')
        plt.title(f'Convolutional Layers Gradient Flow (Growth Rate {growth_rate})')
        plt.grid(True, alpha=0.3)

        # 子图2: 梯度范数分布
        plt.subplot(2, 2, 2)
        all_conv_grads = list(conv_layers.values())
        plt.hist(all_conv_grads, bins=20, alpha=0.7, color='lightgreen')
        plt.xlabel('Gradient L2 Norm')
        plt.ylabel('Frequency')
        plt.title('Gradient Norm Distribution')
        plt.grid(True, alpha=0.3)

        # 子图3: 按DenseBlock分组的梯度
        plt.subplot(2, 2, 3)
        block_gradients = defaultdict(list)
        for name, grad_norm in conv_layers.items():
            if 'denseblock' in name:
                block_num = name.split('.')[1]  # 提取block编号
                block_gradients[f'Block {block_num}'].append(grad_norm)

        if block_gradients:
            block_names = list(block_gradients.keys())
            block_avg_grads = [np.mean(grads) for grads in block_gradients.values()]

            plt.bar(block_names, block_avg_grads, color='salmon')
            plt.ylabel('Average Gradient L2 Norm')
            plt.title('Gradient Flow by Dense Block')
            plt.xticks(rotation=45)
            plt.grid(True, alpha=0.3)

        # 子图4: 梯度统计信息
        plt.subplot(2, 2, 4)
        stats_text = f"""
        Growth Rate: {growth_rate}
        Total Conv Layers: {len(conv_layers)}
        Max Gradient: {max(conv_layers.values()):.6f}
        Min Gradient: {min(conv_layers.values()):.6f}
        Mean Gradient: {np.mean(list(conv_layers.values())):.6f}
        Std Gradient: {np.std(list(conv_layers.values())):.6f}
        """
        plt.text(0.1, 0.5, stats_text, fontsize=12, verticalalignment='center')
        plt.axis('off')
        plt.title('Gradient Statistics')

        plt.tight_layout()
        plt.savefig(f'gradient_analysis_growth_rate_{growth_rate}.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    """主函数：分析不同growth_rate的梯度流"""
    # 数据预处理
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010))
    ])

    # 加载CIFAR-10数据集（只用少量数据进行梯度分析）
    trainset = torchvision.datasets.CIFAR10(root='./data', train=True, download=True, transform=transform)
    trainloader = torch.utils.data.DataLoader(trainset, batch_size=32, shuffle=True, num_workers=2)

    criterion = nn.CrossEntropyLoss()
    growth_rates = [12, 24, 32]

    print("Starting gradient flow analysis for different growth rates...")

    for growth_rate in growth_rates:
        print(f"\n{'='*50}")
        print(f"Analyzing Growth Rate: {growth_rate}")
        print(f"{'='*50}")

        # 创建模型
        model = create_densenet_cifar10(growth_rate=growth_rate).to(device)

        # 分析梯度流
        gradient_stats = analyze_gradient_flow(model, trainloader, criterion, num_batches=3)

        # 可视化结果
        plot_gradient_analysis(gradient_stats, growth_rate)

        print(f"Gradient analysis completed for growth rate {growth_rate}")

    print("\nAll gradient flow analyses completed!")

if __name__ == "__main__":
    main()
